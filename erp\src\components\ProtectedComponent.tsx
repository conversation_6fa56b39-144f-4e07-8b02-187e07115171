import React from 'react';
import { Permission, UserRole } from '../types/auth';
import { usePermissions } from '../hooks/usePermissions';

interface ProtectedComponentProps {
  children: React.ReactNode;
  requiredPermission?: Permission;
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
  requiredRoles?: UserRole[];
  requireAll?: boolean; // true: cần tất cả permissions, false: cần ít nhất 1 permission
  fallback?: React.ReactNode;
  departmentId?: string; // Đ<PERSON> check quyền truy cập department cụ thể
  roomId?: string; // Đ<PERSON> check quyền truy cập room cụ thể
}

/**
 * Component bảo vệ dựa trên quyền hạn
 * Chỉ hiển thị children nếu user có đủ quyền
 */
export const ProtectedComponent: React.FC<ProtectedComponentProps> = ({
  children,
  requiredPermission,
  requiredPermissions = [],
  requiredRole,
  requiredRoles = [],
  requireAll = false,
  fallback = null,
  departmentId,
  roomId
}) => {
  const { 
    hasPermission, 
    hasAnyPermission, 
    hasAllPermissions,
    canViewDepartment,
    canViewRoom,
    user 
  } = usePermissions();

  // Kiểm tra quyền cơ bản
  const checkBasicPermissions = (): boolean => {
    // Kiểm tra single permission
    if (requiredPermission && !hasPermission(requiredPermission)) {
      return false;
    }

    // Kiểm tra multiple permissions
    if (requiredPermissions.length > 0) {
      if (requireAll) {
        if (!hasAllPermissions(requiredPermissions)) return false;
      } else {
        if (!hasAnyPermission(requiredPermissions)) return false;
      }
    }

    // Kiểm tra single role
    if (requiredRole && user?.role !== requiredRole) {
      return false;
    }

    // Kiểm tra multiple roles
    if (requiredRoles.length > 0 && user && !requiredRoles.includes(user.role)) {
      return false;
    }

    return true;
  };

  // Kiểm tra quyền truy cập department/room cụ thể
  const checkResourceAccess = (): boolean => {
    // Kiểm tra quyền truy cập department
    if (departmentId && !canViewDepartment(departmentId)) {
      return false;
    }

    // Kiểm tra quyền truy cập room
    if (roomId && !canViewRoom(roomId, departmentId)) {
      return false;
    }

    return true;
  };

  // Kiểm tra tất cả điều kiện
  const hasAccess = checkBasicPermissions() && checkResourceAccess();

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Component hiển thị thông báo không có quyền truy cập
 */
export const AccessDenied: React.FC<{ message?: string }> = ({ 
  message = "Bạn không có quyền truy cập chức năng này" 
}) => {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center">
        <div className="text-6xl mb-4">🔒</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Không có quyền truy cập
        </h3>
        <p className="text-gray-600">{message}</p>
      </div>
    </div>
  );
};

/**
 * HOC để bảo vệ component
 */
export const withPermission = (
  Component: React.ComponentType<any>,
  requiredPermission: Permission,
  fallback?: React.ReactNode
) => {
  return (props: any) => (
    <ProtectedComponent 
      requiredPermission={requiredPermission} 
      fallback={fallback || <AccessDenied />}
    >
      <Component {...props} />
    </ProtectedComponent>
  );
};

/**
 * HOC để bảo vệ component với multiple permissions
 */
export const withPermissions = (
  Component: React.ComponentType<any>,
  requiredPermissions: Permission[],
  requireAll: boolean = false,
  fallback?: React.ReactNode
) => {
  return (props: any) => (
    <ProtectedComponent 
      requiredPermissions={requiredPermissions}
      requireAll={requireAll}
      fallback={fallback || <AccessDenied />}
    >
      <Component {...props} />
    </ProtectedComponent>
  );
};

/**
 * HOC để bảo vệ component với role
 */
export const withRole = (
  Component: React.ComponentType<any>,
  requiredRole: UserRole,
  fallback?: React.ReactNode
) => {
  return (props: any) => (
    <ProtectedComponent 
      requiredRole={requiredRole}
      fallback={fallback || <AccessDenied />}
    >
      <Component {...props} />
    </ProtectedComponent>
  );
};

export default ProtectedComponent;
