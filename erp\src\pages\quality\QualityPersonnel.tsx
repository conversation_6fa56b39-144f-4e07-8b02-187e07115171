import { useState } from 'react';
import { Eye, EyeOff, Edit, Trash2, Plus, Info, X } from 'lucide-react';

// Interface cho dữ liệu nhân sự quản lý
interface Personnel {
  id: string;
  fullName: string;
  username: string;
  password: string;
  position: string;
}

// Interface cho thông tin nhân sự chi tiết
interface PersonnelInfo {
  id: string;
  employeeId: string;
  fullName: string;
  gender: string;
  department: string;
  room: string;
  position: string;
  weight: number;
  height: number;
  shirtSize: string;
  pantsSize: string;
  shoeSize: string;
  personalPhone: string;
  bankAccount: string;
  lockerNumber: string;
  currentStatus: string;
  notes: string;
}

// Interface cho bảng lương nhân sự
interface SalaryInfo {
  id: string;
  employeeId: string;
  fullName: string;
  gender: string;
  department: string;
  room: string;
  position: string;
  basicSalary: number;
  kpiSalary: number;
  kpiBonus: number;
  kpiPenalty: number;
  totalSalary: number;
  employeeRank: string;
  evaluation: string;
}

const QualityPersonnel = () => {
  const [activeSection, setActiveSection] = useState<string>('');
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [selectedPersonnel, setSelectedPersonnel] = useState<PersonnelInfo | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPersonnel, setEditingPersonnel] = useState<PersonnelInfo | null>(null);

  // Dữ liệu mẫu cho nhân sự quản lý
  const [personnelData, setPersonnelData] = useState<Personnel[]>([
    {
      id: '1',
      fullName: 'Nguyễn Văn An',
      username: 'nvan.an',
      password: 'password123',
      position: 'Trưởng phòng Chất lượng'
    },
    {
      id: '2',
      fullName: 'Trần Thị Bình',
      username: 'tthi.binh',
      password: 'secure456',
      position: 'Chuyên viên Kiểm tra chất lượng'
    },
    {
      id: '3',
      fullName: 'Lê Minh Cường',
      username: 'lminh.cuong',
      password: 'mypass789',
      position: 'Nhân viên Kiểm định'
    }
  ]);

  // Dữ liệu mẫu cho thông tin nhân sự chi tiết
  const [personnelInfoData, setPersonnelInfoData] = useState<PersonnelInfo[]>([
    {
      id: '1',
      employeeId: 'QC001',
      fullName: 'Nguyễn Văn An',
      gender: 'Nam',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Trưởng phòng Chất lượng',
      weight: 70,
      height: 175,
      shirtSize: 'L',
      pantsSize: '32',
      shoeSize: '42',
      personalPhone: '**********',
      bankAccount: '**********',
      lockerNumber: 'L001',
      currentStatus: 'Đang làm việc',
      notes: 'Nhân viên xuất sắc'
    },
    {
      id: '2',
      employeeId: 'QC002',
      fullName: 'Trần Thị Bình',
      gender: 'Nữ',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Chuyên viên Kiểm tra chất lượng',
      weight: 55,
      height: 160,
      shirtSize: 'M',
      pantsSize: '28',
      shoeSize: '37',
      personalPhone: '**********',
      bankAccount: '**********',
      lockerNumber: 'L002',
      currentStatus: 'Đang làm việc',
      notes: 'Chuyên môn tốt'
    },
    {
      id: '3',
      employeeId: 'QC003',
      fullName: 'Lê Minh Cường',
      gender: 'Nam',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Nhân viên Kiểm định',
      weight: 68,
      height: 172,
      shirtSize: 'L',
      pantsSize: '31',
      shoeSize: '41',
      personalPhone: '**********',
      bankAccount: '**********',
      lockerNumber: 'L003',
      currentStatus: 'Đang làm việc',
      notes: 'Tích cực học hỏi'
    }
  ]);

  // Dữ liệu mẫu cho bảng lương nhân sự
  const [salaryData, setSalaryData] = useState<SalaryInfo[]>([
    {
      id: '1',
      employeeId: 'QC001',
      fullName: 'Nguyễn Văn An',
      gender: 'Nam',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Trưởng phòng Chất lượng',
      basicSalary: ********,
      kpiSalary: 3000000,
      kpiBonus: 500000,
      kpiPenalty: 0,
      totalSalary: ********,
      employeeRank: 'A',
      evaluation: 'Xuất sắc'
    },
    {
      id: '2',
      employeeId: 'QC002',
      fullName: 'Trần Thị Bình',
      gender: 'Nữ',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Chuyên viên Kiểm tra chất lượng',
      basicSalary: ********,
      kpiSalary: 2400000,
      kpiBonus: 300000,
      kpiPenalty: 0,
      totalSalary: 14700000,
      employeeRank: 'B+',
      evaluation: 'Tốt'
    },
    {
      id: '3',
      employeeId: 'QC003',
      fullName: 'Lê Minh Cường',
      gender: 'Nam',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Nhân viên Kiểm định',
      basicSalary: 10000000,
      kpiSalary: 2000000,
      kpiBonus: 200000,
      kpiPenalty: 100000,
      totalSalary: 12100000,
      employeeRank: 'B',
      evaluation: 'Khá'
    }
  ]);

  const handleButtonClick = (section: string) => {
    setActiveSection(section);
  };

  const togglePasswordVisibility = (id: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const handleEdit = (id: string) => {
    const person = personnelInfoData.find(p => p.id === id);
    if (person) {
      setEditingPersonnel({ ...person });
      setIsEditModalOpen(true);
    }
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa nhân sự này?')) {
      // Xóa từ tất cả các danh sách
      setPersonnelData(prev => prev.filter(person => person.id !== id));
      setPersonnelInfoData(prev => prev.filter(person => person.id !== id));
      setSalaryData(prev => prev.filter(person => person.id !== id));
    }
  };

  const handleAddNew = () => {
    console.log('Add new personnel');
    // TODO: Implement add new functionality
  };

  const handleViewDetails = (id: string) => {
    const person = personnelInfoData.find(p => p.id === id);
    if (person) {
      setSelectedPersonnel(person);
      setIsModalOpen(true);
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedPersonnel(null);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setEditingPersonnel(null);
  };

  const handleInputChange = (field: keyof PersonnelInfo, value: string | number) => {
    if (editingPersonnel) {
      setEditingPersonnel({
        ...editingPersonnel,
        [field]: value
      });
    }
  };

  const handleSaveEdit = () => {
    if (editingPersonnel) {
      setPersonnelInfoData(prev =>
        prev.map(person =>
          person.id === editingPersonnel.id ? editingPersonnel : person
        )
      );
      closeEditModal();
    }
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'responsibility':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">Danh sách trách nhiệm</h2>
            <p>Nội dung danh sách trách nhiệm của nhân sự bộ phận chất lượng sẽ được hiển thị ở đây.</p>
          </div>
        );
      case 'personnel-info':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Thông tin nhân sự</h2>
              <button
                onClick={handleAddNew}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm thông tin
              </button>
            </div>

            <div className="overflow-hidden">
              <table className="w-full table-auto text-sm">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                      STT
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Mã NV
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tên nhân viên
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                      Giới tính
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Bộ phận
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Phòng ban
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vị trí
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                      SĐT
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                      TK ngân hàng
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Hiện trạng
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ghi chú
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {personnelInfoData.map((person, index) => (
                    <tr key={person.id} className="hover:bg-gray-50">
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                        {index + 1}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        {person.employeeId}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.fullName}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex px-1 py-1 text-xs font-semibold rounded-full ${
                          person.gender === 'Nam' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'
                        }`}>
                          {person.gender}
                        </span>
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.department}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.room}
                      </td>
                      <td className="px-2 py-3 text-sm text-gray-900">
                        <span className="inline-flex px-1 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                          {person.position}
                        </span>
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.personalPhone}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.bankAccount}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex px-1 py-1 text-xs font-semibold rounded-full ${
                          person.currentStatus === 'Đang làm việc'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {person.currentStatus}
                        </span>
                      </td>
                      <td className="px-2 py-3 text-sm text-gray-900 max-w-20 truncate">
                        {person.notes}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-1">
                          <button
                            onClick={() => handleViewDetails(person.id)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Xem chi tiết"
                          >
                            <Info className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleEdit(person.id)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Chỉnh sửa"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(person.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Xóa"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {personnelInfoData.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">Chưa có dữ liệu thông tin nhân sự</p>
              </div>
            )}
          </div>
        );
      case 'personnel-management':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Quản lý nhân sự</h2>
              <button
                onClick={handleAddNew}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm nhân sự
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full table-auto">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      STT
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Họ và tên
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tài khoản
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Mật khẩu
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vị trí
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {personnelData.map((person, index) => (
                    <tr key={person.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {person.fullName}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {person.username}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-900">
                            {showPasswords[person.id] ? person.password : '••••••••'}
                          </span>
                          <button
                            onClick={() => togglePasswordVisibility(person.id)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            {showPasswords[person.id] ? (
                              <EyeOff className="w-4 h-4" />
                            ) : (
                              <Eye className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {person.position}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(person.id)}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(person.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {personnelData.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">Chưa có dữ liệu nhân sự</p>
              </div>
            )}
          </div>
        );
      case 'salary':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Bảng lương nhân sự</h2>
              <button
                onClick={handleAddNew}
                className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm bảng lương
              </button>
            </div>

            <div className="overflow-hidden">
              <table className="w-full table-auto text-sm">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                      STT
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Mã nhân viên
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tên nhân viên
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                      Giới tính
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Bộ phận
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Phòng ban
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vị trí
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                      Lương cơ bản
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                      Lương KPI
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Lương KPI bị trừ
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                      Lương KPI cộng lại
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                      Tổng lương thực nhận
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Hạng nhân viên
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Bảng đánh giá
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {salaryData.map((person, index) => (
                    <tr key={person.id} className="hover:bg-gray-50">
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                        {index + 1}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        {person.employeeId}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.fullName}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex px-1 py-1 text-xs font-semibold rounded-full ${
                          person.gender === 'Nam' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'
                        }`}>
                          {person.gender}
                        </span>
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.department}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900">
                        {person.room}
                      </td>
                      <td className="px-2 py-3 text-sm text-gray-900">
                        <span className="inline-flex px-1 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                          {person.position}
                        </span>
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right">
                        {person.basicSalary.toLocaleString('vi-VN')}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-right">
                        {person.kpiSalary.toLocaleString('vi-VN')}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-red-600 text-right">
                        -{person.kpiPenalty.toLocaleString('vi-VN')}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-green-600 text-right">
                        +{person.kpiBonus.toLocaleString('vi-VN')}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm font-semibold text-gray-900 text-right">
                        {person.totalSalary.toLocaleString('vi-VN')}
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          person.employeeRank === 'A' ? 'bg-green-100 text-green-800' :
                          person.employeeRank.startsWith('B') ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {person.employeeRank}
                        </span>
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                        <span className={`inline-flex px-1 py-1 text-xs font-semibold rounded-full ${
                          person.evaluation === 'Xuất sắc' ? 'bg-green-100 text-green-800' :
                          person.evaluation === 'Tốt' ? 'bg-blue-100 text-blue-800' :
                          person.evaluation === 'Khá' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {person.evaluation}
                        </span>
                      </td>
                      <td className="px-2 py-3 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-1">
                          <button
                            onClick={() => handleEdit(person.id)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Chỉnh sửa"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(person.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Xóa"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {salaryData.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">Chưa có dữ liệu bảng lương</p>
              </div>
            )}
          </div>
        );
      default:
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <p className="text-gray-500">Vui lòng chọn một mục để xem nội dung chi tiết.</p>
          </div>
        );
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Nhân sự - Bộ phận Chất lượng</h1>

      {/* Button Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <button
          onClick={() => handleButtonClick('responsibility')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'responsibility'
              ? 'bg-blue-500 text-white border-blue-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400 hover:bg-blue-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">📋</div>
            <div className="font-semibold">Danh sách trách nhiệm</div>
          </div>
        </button>

        <button
          onClick={() => handleButtonClick('personnel-info')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'personnel-info'
              ? 'bg-green-500 text-white border-green-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-green-400 hover:bg-green-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">👥</div>
            <div className="font-semibold">Thông tin nhân sự</div>
          </div>
        </button>

        <button
          onClick={() => handleButtonClick('personnel-management')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'personnel-management'
              ? 'bg-purple-500 text-white border-purple-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:bg-purple-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">⚙️</div>
            <div className="font-semibold">Quản lý nhân sự</div>
          </div>
        </button>

        <button
          onClick={() => handleButtonClick('salary')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'salary'
              ? 'bg-orange-500 text-white border-orange-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-orange-400 hover:bg-orange-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">💰</div>
            <div className="font-semibold">Bảng lương nhân sự</div>
          </div>
        </button>
      </div>

      {/* Content Area */}
      {renderContent()}

      {/* Modal for Personnel Details */}
      {isModalOpen && selectedPersonnel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Chi tiết thông tin nhân sự
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800 border-b pb-2">Thông tin cơ bản</h4>
                <div>
                  <label className="text-sm font-medium text-gray-500">Mã nhân viên:</label>
                  <p className="text-gray-900">{selectedPersonnel.employeeId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Họ và tên:</label>
                  <p className="text-gray-900">{selectedPersonnel.fullName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Giới tính:</label>
                  <p className="text-gray-900">{selectedPersonnel.gender}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Bộ phận:</label>
                  <p className="text-gray-900">{selectedPersonnel.department}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Phòng ban:</label>
                  <p className="text-gray-900">{selectedPersonnel.room}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Vị trí:</label>
                  <p className="text-gray-900">{selectedPersonnel.position}</p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800 border-b pb-2">Thông tin vóc dáng</h4>
                <div>
                  <label className="text-sm font-medium text-gray-500">Cân nặng:</label>
                  <p className="text-gray-900">{selectedPersonnel.weight} kg</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Chiều cao:</label>
                  <p className="text-gray-900">{selectedPersonnel.height} cm</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Size áo:</label>
                  <p className="text-gray-900">{selectedPersonnel.shirtSize}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Size quần:</label>
                  <p className="text-gray-900">{selectedPersonnel.pantsSize}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Size giày/dép:</label>
                  <p className="text-gray-900">{selectedPersonnel.shoeSize}</p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800 border-b pb-2">Thông tin liên lạc & khác</h4>
                <div>
                  <label className="text-sm font-medium text-gray-500">SĐT cá nhân:</label>
                  <p className="text-gray-900">{selectedPersonnel.personalPhone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Số TK ngân hàng:</label>
                  <p className="text-gray-900">{selectedPersonnel.bankAccount}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Số tủ cá nhân:</label>
                  <p className="text-gray-900">{selectedPersonnel.lockerNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Hiện trạng:</label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    selectedPersonnel.currentStatus === 'Đang làm việc'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {selectedPersonnel.currentStatus}
                  </span>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Ghi chú:</label>
                  <p className="text-gray-900">{selectedPersonnel.notes}</p>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal for Edit Personnel */}
      {isEditModalOpen && editingPersonnel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Chỉnh sửa thông tin nhân sự
              </h3>
              <button
                onClick={closeEditModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form onSubmit={(e) => { e.preventDefault(); handleSaveEdit(); }}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Thông tin cơ bản */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800 border-b pb-2">Thông tin cơ bản</h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Mã nhân viên <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.employeeId}
                      onChange={(e) => handleInputChange('employeeId', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Họ và tên <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.fullName}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Giới tính <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={editingPersonnel.gender}
                      onChange={(e) => handleInputChange('gender', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">Chọn giới tính</option>
                      <option value="Nam">Nam</option>
                      <option value="Nữ">Nữ</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Bộ phận <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.department}
                      onChange={(e) => handleInputChange('department', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phòng ban <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.room}
                      onChange={(e) => handleInputChange('room', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Vị trí <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.position}
                      onChange={(e) => handleInputChange('position', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                {/* Thông tin vóc dáng */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800 border-b pb-2">Thông tin vóc dáng</h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Cân nặng (kg)
                    </label>
                    <input
                      type="number"
                      value={editingPersonnel.weight}
                      onChange={(e) => handleInputChange('weight', Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      step="0.1"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Chiều cao (cm)
                    </label>
                    <input
                      type="number"
                      value={editingPersonnel.height}
                      onChange={(e) => handleInputChange('height', Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Size áo
                    </label>
                    <select
                      value={editingPersonnel.shirtSize}
                      onChange={(e) => handleInputChange('shirtSize', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Chọn size áo</option>
                      <option value="XS">XS</option>
                      <option value="S">S</option>
                      <option value="M">M</option>
                      <option value="L">L</option>
                      <option value="XL">XL</option>
                      <option value="XXL">XXL</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Size quần
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.pantsSize}
                      onChange={(e) => handleInputChange('pantsSize', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="VD: 28, 30, 32..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Size giày/dép
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.shoeSize}
                      onChange={(e) => handleInputChange('shoeSize', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="VD: 37, 38, 39..."
                    />
                  </div>
                </div>

                {/* Thông tin liên lạc & khác */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800 border-b pb-2">Thông tin liên lạc & khác</h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SĐT cá nhân <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="tel"
                      value={editingPersonnel.personalPhone}
                      onChange={(e) => handleInputChange('personalPhone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                      placeholder="VD: **********"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Số TK ngân hàng
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.bankAccount}
                      onChange={(e) => handleInputChange('bankAccount', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="VD: **********"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Số tủ cá nhân
                    </label>
                    <input
                      type="text"
                      value={editingPersonnel.lockerNumber}
                      onChange={(e) => handleInputChange('lockerNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="VD: L001"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hiện trạng <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={editingPersonnel.currentStatus}
                      onChange={(e) => handleInputChange('currentStatus', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">Chọn hiện trạng</option>
                      <option value="Đang làm việc">Đang làm việc</option>
                      <option value="Nghỉ phép">Nghỉ phép</option>
                      <option value="Nghỉ việc">Nghỉ việc</option>
                      <option value="Thử việc">Thử việc</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ghi chú
                    </label>
                    <textarea
                      value={editingPersonnel.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Nhập ghi chú..."
                    />
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={closeEditModal}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Hủy
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Lưu thay đổi
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default QualityPersonnel;
