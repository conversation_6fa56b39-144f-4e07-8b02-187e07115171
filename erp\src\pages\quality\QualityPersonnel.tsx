import { useState } from 'react';
import { <PERSON>, EyeOff, Edit, Trash2, Plus } from 'lucide-react';

// Interface cho dữ liệu nhân sự quản lý
interface Personnel {
  id: string;
  fullName: string;
  username: string;
  password: string;
  position: string;
}

// Interface cho thông tin nhân sự chi tiết
interface PersonnelInfo {
  id: string;
  employeeId: string;
  fullName: string;
  gender: string;
  department: string;
  room: string;
  position: string;
  weight: number;
  height: number;
  shirtSize: string;
  pantsSize: string;
  shoeSize: string;
  personalPhone: string;
  bankAccount: string;
  lockerNumber: string;
  currentStatus: string;
  notes: string;
}

const QualityPersonnel = () => {
  const [activeSection, setActiveSection] = useState<string>('');
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});

  // Dữ liệu mẫu cho nhân sự quản lý
  const [personnelData, setPersonnelData] = useState<Personnel[]>([
    {
      id: '1',
      fullName: '<PERSON><PERSON><PERSON><PERSON>',
      username: 'nvan.an',
      password: 'password123',
      position: 'Trưởng phòng Chất lượng'
    },
    {
      id: '2',
      fullName: 'Trần Thị Bình',
      username: 'tthi.binh',
      password: 'secure456',
      position: 'Chuyên viên Kiểm tra chất lượng'
    },
    {
      id: '3',
      fullName: 'Lê Minh Cường',
      username: 'lminh.cuong',
      password: 'mypass789',
      position: 'Nhân viên Kiểm định'
    }
  ]);

  // Dữ liệu mẫu cho thông tin nhân sự chi tiết
  const [personnelInfoData, setPersonnelInfoData] = useState<PersonnelInfo[]>([
    {
      id: '1',
      employeeId: 'QC001',
      fullName: 'Nguyễn Văn An',
      gender: 'Nam',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Trưởng phòng Chất lượng',
      weight: 70,
      height: 175,
      shirtSize: 'L',
      pantsSize: '32',
      shoeSize: '42',
      personalPhone: '**********',
      bankAccount: '**********',
      lockerNumber: 'L001',
      currentStatus: 'Đang làm việc',
      notes: 'Nhân viên xuất sắc'
    },
    {
      id: '2',
      employeeId: 'QC002',
      fullName: 'Trần Thị Bình',
      gender: 'Nữ',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Chuyên viên Kiểm tra chất lượng',
      weight: 55,
      height: 160,
      shirtSize: 'M',
      pantsSize: '28',
      shoeSize: '37',
      personalPhone: '**********',
      bankAccount: '**********',
      lockerNumber: 'L002',
      currentStatus: 'Đang làm việc',
      notes: 'Chuyên môn tốt'
    },
    {
      id: '3',
      employeeId: 'QC003',
      fullName: 'Lê Minh Cường',
      gender: 'Nam',
      department: 'Chất lượng',
      room: 'Phòng QC',
      position: 'Nhân viên Kiểm định',
      weight: 68,
      height: 172,
      shirtSize: 'L',
      pantsSize: '31',
      shoeSize: '41',
      personalPhone: '**********',
      bankAccount: '**********',
      lockerNumber: 'L003',
      currentStatus: 'Đang làm việc',
      notes: 'Tích cực học hỏi'
    }
  ]);

  const handleButtonClick = (section: string) => {
    setActiveSection(section);
  };

  const togglePasswordVisibility = (id: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const handleEdit = (id: string) => {
    console.log('Edit personnel with id:', id);
    // TODO: Implement edit functionality
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa nhân sự này?')) {
      // Xóa từ cả hai danh sách
      setPersonnelData(prev => prev.filter(person => person.id !== id));
      setPersonnelInfoData(prev => prev.filter(person => person.id !== id));
    }
  };

  const handleAddNew = () => {
    console.log('Add new personnel');
    // TODO: Implement add new functionality
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'responsibility':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">Danh sách trách nhiệm</h2>
            <p>Nội dung danh sách trách nhiệm của nhân sự bộ phận chất lượng sẽ được hiển thị ở đây.</p>
          </div>
        );
      case 'personnel-info':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Thông tin nhân sự</h2>
              <button
                onClick={handleAddNew}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm thông tin
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full table-auto text-sm">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      STT
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Mã nhân viên
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tên nhân viên
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Giới tính
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Bộ phận
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Phòng ban
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vị trí
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cân nặng (kg)
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Chiều cao (cm)
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Size áo
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Size quần
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Size giày/dép
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      SĐT cá nhân
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Số TK ngân hàng
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Số tủ cá nhân
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Hiện trạng
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ghi chú
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {personnelInfoData.map((person, index) => (
                    <tr key={person.id} className="hover:bg-gray-50">
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {person.employeeId}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        {person.fullName}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          person.gender === 'Nam' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'
                        }`}>
                          {person.gender}
                        </span>
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        {person.department}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        {person.room}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                          {person.position}
                        </span>
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {person.weight}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {person.height}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {person.shirtSize}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {person.pantsSize}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {person.shoeSize}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        {person.personalPhone}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        {person.bankAccount}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {person.lockerNumber}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          person.currentStatus === 'Đang làm việc'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {person.currentStatus}
                        </span>
                      </td>
                      <td className="px-3 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {person.notes}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(person.id)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Chỉnh sửa"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(person.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Xóa"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {personnelInfoData.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">Chưa có dữ liệu thông tin nhân sự</p>
              </div>
            )}
          </div>
        );
      case 'personnel-management':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Quản lý nhân sự</h2>
              <button
                onClick={handleAddNew}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm nhân sự
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full table-auto">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      STT
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Họ và tên
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tài khoản
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Mật khẩu
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vị trí
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {personnelData.map((person, index) => (
                    <tr key={person.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {person.fullName}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {person.username}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-900">
                            {showPasswords[person.id] ? person.password : '••••••••'}
                          </span>
                          <button
                            onClick={() => togglePasswordVisibility(person.id)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            {showPasswords[person.id] ? (
                              <EyeOff className="w-4 h-4" />
                            ) : (
                              <Eye className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {person.position}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(person.id)}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(person.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {personnelData.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">Chưa có dữ liệu nhân sự</p>
              </div>
            )}
          </div>
        );
      case 'salary':
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">Bảng lương nhân sự</h2>
            <p>Nội dung bảng lương nhân sự bộ phận chất lượng sẽ được hiển thị ở đây.</p>
          </div>
        );
      default:
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <p className="text-gray-500">Vui lòng chọn một mục để xem nội dung chi tiết.</p>
          </div>
        );
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Nhân sự - Bộ phận Chất lượng</h1>

      {/* Button Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <button
          onClick={() => handleButtonClick('responsibility')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'responsibility'
              ? 'bg-blue-500 text-white border-blue-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400 hover:bg-blue-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">📋</div>
            <div className="font-semibold">Danh sách trách nhiệm</div>
          </div>
        </button>

        <button
          onClick={() => handleButtonClick('personnel-info')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'personnel-info'
              ? 'bg-green-500 text-white border-green-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-green-400 hover:bg-green-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">👥</div>
            <div className="font-semibold">Thông tin nhân sự</div>
          </div>
        </button>

        <button
          onClick={() => handleButtonClick('personnel-management')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'personnel-management'
              ? 'bg-purple-500 text-white border-purple-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:bg-purple-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">⚙️</div>
            <div className="font-semibold">Quản lý nhân sự</div>
          </div>
        </button>

        <button
          onClick={() => handleButtonClick('salary')}
          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
            activeSection === 'salary'
              ? 'bg-orange-500 text-white border-orange-500 shadow-lg'
              : 'bg-white text-gray-700 border-gray-300 hover:border-orange-400 hover:bg-orange-50'
          }`}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">💰</div>
            <div className="font-semibold">Bảng lương nhân sự</div>
          </div>
        </button>
      </div>

      {/* Content Area */}
      {renderContent()}
    </div>
  );
};

export default QualityPersonnel;
