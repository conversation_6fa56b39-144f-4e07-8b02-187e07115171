import { useContext } from 'react';
import { AuthContext } from '../contexts/AuthContext';
import { Permission, hasPermission, hasAnyPermission, hasAllPermissions } from '../types/auth';

/**
 * Hook để kiểm tra quyền hạn của user hiện tại
 */
export const usePermissions = () => {
  const { user } = useContext(AuthContext);

  /**
   * Kiểm tra user có quyền cụ thể không
   */
  const checkPermission = (permission: Permission): boolean => {
    if (!user) return false;
    return hasPermission(user.role, permission);
  };

  /**
   * Kiểm tra user có ít nhất một trong các quyền không
   */
  const checkAnyPermission = (permissions: Permission[]): boolean => {
    if (!user) return false;
    return hasAnyPermission(user.role, permissions);
  };

  /**
   * Kiểm tra user có tất cả các quyền không
   */
  const checkAllPermissions = (permissions: Permission[]): boolean => {
    if (!user) return false;
    return hasAllPermissions(user.role, permissions);
  };

  /**
   * Kiểm tra user có thể xem dữ liệu của department cụ thể không
   */
  const canViewDepartment = (departmentId: string): boolean => {
    if (!user) return false;
    
    // Giám đốc xem được tất cả
    if (checkPermission('view_all')) return true;
    
    // Trưởng bộ phận chỉ xem được bộ phận của mình
    if (checkPermission('view_department') && user.department === departmentId) return true;
    
    return false;
  };

  /**
   * Kiểm tra user có thể xem dữ liệu của room cụ thể không
   */
  const canViewRoom = (roomId: string, departmentId?: string): boolean => {
    if (!user) return false;
    
    // Giám đốc xem được tất cả
    if (checkPermission('view_all')) return true;
    
    // Trưởng bộ phận xem được các phòng trong bộ phận
    if (checkPermission('view_department') && user.department === departmentId) return true;
    
    // Trưởng phòng chỉ xem được phòng của mình
    if (checkPermission('view_room') && user.subDepartment === roomId) return true;
    
    return false;
  };

  /**
   * Kiểm tra user có thể quản lý nhân sự của department cụ thể không
   */
  const canManageDepartmentStaff = (departmentId: string): boolean => {
    if (!user) return false;
    
    // Giám đốc quản lý được tất cả
    if (checkPermission('manage_all_staff')) return true;
    
    // Trưởng bộ phận chỉ quản lý được bộ phận của mình
    if (checkPermission('manage_department_staff') && user.department === departmentId) return true;
    
    return false;
  };

  /**
   * Kiểm tra user có thể quản lý nhân sự của room cụ thể không
   */
  const canManageRoomStaff = (roomId: string, departmentId?: string): boolean => {
    if (!user) return false;
    
    // Giám đốc quản lý được tất cả
    if (checkPermission('manage_all_staff')) return true;
    
    // Trưởng bộ phận quản lý được các phòng trong bộ phận
    if (checkPermission('manage_department_staff') && user.department === departmentId) return true;
    
    // Trưởng phòng chỉ quản lý được phòng của mình
    if (checkPermission('manage_room_staff') && user.subDepartment === roomId) return true;
    
    return false;
  };

  /**
   * Kiểm tra user có thể xem lương của department cụ thể không
   */
  const canViewDepartmentSalary = (departmentId: string): boolean => {
    if (!user) return false;
    
    // Giám đốc xem được tất cả
    if (checkPermission('view_all_salary')) return true;
    
    // Trưởng bộ phận chỉ xem được lương bộ phận của mình
    if (checkPermission('view_department_salary') && user.department === departmentId) return true;
    
    return false;
  };

  /**
   * Kiểm tra user có thể xem lương của room cụ thể không
   */
  const canViewRoomSalary = (roomId: string, departmentId?: string): boolean => {
    if (!user) return false;
    
    // Giám đốc xem được tất cả
    if (checkPermission('view_all_salary')) return true;
    
    // Trưởng bộ phận xem được lương các phòng trong bộ phận
    if (checkPermission('view_department_salary') && user.department === departmentId) return true;
    
    // Trưởng phòng chỉ xem được lương phòng của mình
    if (checkPermission('view_room_salary') && user.subDepartment === roomId) return true;
    
    return false;
  };

  /**
   * Lấy danh sách departments mà user có thể truy cập
   */
  const getAccessibleDepartments = (allDepartments: string[]): string[] => {
    if (!user) return [];
    
    // Giám đốc truy cập được tất cả
    if (checkPermission('view_all')) return allDepartments;
    
    // Trưởng bộ phận chỉ truy cập được bộ phận của mình
    if (checkPermission('view_department') && user.department) {
      return [user.department];
    }
    
    return [];
  };

  /**
   * Lấy danh sách rooms mà user có thể truy cập
   */
  const getAccessibleRooms = (allRooms: Array<{id: string, departmentId: string}>): string[] => {
    if (!user) return [];
    
    // Giám đốc truy cập được tất cả
    if (checkPermission('view_all')) return allRooms.map(room => room.id);
    
    // Trưởng bộ phận truy cập được các phòng trong bộ phận
    if (checkPermission('view_department') && user.department) {
      return allRooms
        .filter(room => room.departmentId === user.department)
        .map(room => room.id);
    }
    
    // Trưởng phòng chỉ truy cập được phòng của mình
    if (checkPermission('view_room') && user.subDepartment) {
      return [user.subDepartment];
    }
    
    return [];
  };

  return {
    // Basic permission checks
    hasPermission: checkPermission,
    hasAnyPermission: checkAnyPermission,
    hasAllPermissions: checkAllPermissions,
    
    // Department & Room access
    canViewDepartment,
    canViewRoom,
    canManageDepartmentStaff,
    canManageRoomStaff,
    canViewDepartmentSalary,
    canViewRoomSalary,
    
    // Get accessible resources
    getAccessibleDepartments,
    getAccessibleRooms,
    
    // User info
    user,
    isLoggedIn: !!user
  };
};

export default usePermissions;
