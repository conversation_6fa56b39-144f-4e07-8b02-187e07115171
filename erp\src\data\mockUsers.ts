import { User, UserRole, Department } from '../types/auth';

// Mock users với các role khác nhau để test phân quyền
export const mockUsers: User[] = [
  {
    _id: '1',
    username: 'director',
    email: '<EMAIL>',
    firstName: '<PERSON>uyễn',
    lastName: '<PERSON><PERSON><PERSON>',
    role: UserRole.DIRECTOR,
    department: undefined, // Gi<PERSON>m đốc không thuộc bộ phận cụ thể
    position: '<PERSON>i<PERSON><PERSON> đốc điều hành',
    employeeCode: 'DIR001',
    gender: 'Nam',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '2',
    username: 'quality_head',
    email: '<EMAIL>',
    firstName: 'Trần',
    lastName: 'Thị Chất Lượng',
    role: UserRole.DEPARTMENT_HEAD,
    department: Department.QUALITY,
    position: 'Trưởng bộ phận Chất lượng',
    employeeCode: 'QH001',
    gender: 'Nữ',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '3',
    username: 'production_head',
    email: '<EMAIL>',
    firstName: 'Lê',
    lastName: 'Văn Sản Xuất',
    role: UserRole.DEPARTMENT_HEAD,
    department: Department.PRODUCTION,
    position: 'Trưởng bộ phận Sản xuất',
    employeeCode: 'PH001',
    gender: 'Nam',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '4',
    username: 'qc_room_head',
    email: '<EMAIL>',
    firstName: 'Phạm',
    lastName: 'Thị QC',
    role: UserRole.ROOM_HEAD,
    department: Department.QUALITY,
    subDepartment: 'qc_room', // Phòng QC
    position: 'Trưởng phòng QC',
    employeeCode: 'QR001',
    gender: 'Nữ',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '5',
    username: 'qa_room_head',
    email: '<EMAIL>',
    firstName: 'Hoàng',
    lastName: 'Văn QA',
    role: UserRole.ROOM_HEAD,
    department: Department.QUALITY,
    subDepartment: 'qa_room', // Phòng QA
    position: 'Trưởng phòng QA',
    employeeCode: 'QA001',
    gender: 'Nam',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '6',
    username: 'qc_employee1',
    email: '<EMAIL>',
    firstName: 'Nguyễn',
    lastName: 'Văn QC1',
    role: UserRole.EMPLOYEE,
    department: Department.QUALITY,
    subDepartment: 'qc_room',
    position: 'Nhân viên QC',
    employeeCode: 'QC001',
    gender: 'Nam',
    weight: 70,
    height: 175,
    shirtSize: 'L',
    pantSize: '32',
    shoeSize: '42',
    personalPhone: '**********',
    bankAccount: '**********',
    lockerNumber: 'L001',
    employeeStatus: 'Đang làm việc',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '7',
    username: 'qc_employee2',
    email: '<EMAIL>',
    firstName: 'Trần',
    lastName: 'Thị QC2',
    role: UserRole.EMPLOYEE,
    department: Department.QUALITY,
    subDepartment: 'qc_room',
    position: 'Nhân viên QC',
    employeeCode: 'QC002',
    gender: 'Nữ',
    weight: 55,
    height: 160,
    shirtSize: 'M',
    pantSize: '28',
    shoeSize: '37',
    personalPhone: '**********',
    bankAccount: '**********',
    lockerNumber: 'L002',
    employeeStatus: 'Đang làm việc',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '8',
    username: 'production_employee',
    email: '<EMAIL>',
    firstName: 'Lê',
    lastName: 'Minh Sản Xuất',
    role: UserRole.EMPLOYEE,
    department: Department.PRODUCTION,
    subDepartment: 'production_line1',
    position: 'Nhân viên sản xuất',
    employeeCode: 'PR001',
    gender: 'Nam',
    weight: 68,
    height: 172,
    shirtSize: 'L',
    pantSize: '31',
    shoeSize: '41',
    personalPhone: '**********',
    bankAccount: '**********',
    lockerNumber: 'L003',
    employeeStatus: 'Đang làm việc',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Helper function để tìm user theo username
export const findUserByUsername = (username: string): User | undefined => {
  return mockUsers.find(user => user.username === username);
};

// Helper function để lấy users theo department
export const getUsersByDepartment = (department: Department): User[] => {
  return mockUsers.filter(user => user.department === department);
};

// Helper function để lấy users theo role
export const getUsersByRole = (role: UserRole): User[] => {
  return mockUsers.filter(user => user.role === role);
};

// Mock login function để test
export const mockLogin = (username: string, password: string): User | null => {
  // Trong thực tế, sẽ verify password
  const user = findUserByUsername(username);
  if (user && password === 'password123') {
    return user;
  }
  return null;
};
