import React, { useState } from 'react';
import { usePermissions } from '../hooks/usePermissions';
import { ProtectedComponent, AccessDenied } from './ProtectedComponent';
import { UserRole, Permission, getRoleDisplayName } from '../types/auth';
import { mockUsers, mockLogin } from '../data/mockUsers';
import { useAuth } from '../contexts/AuthContext';

/**
 * Component demo để test hệ thống phân quyền
 */
const PermissionDemo: React.FC = () => {
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [loginStatus, setLoginStatus] = useState<string>('');
  const { user, logout } = useAuth();
  const {
    hasPermission,
    canViewDepartment,
    canManageDepartmentStaff,
    canViewDepartmentSalary
  } = usePermissions();

  const handleLogin = async (username: string) => {
    try {
      const mockUser = mockLogin(username, 'password123');
      if (mockUser) {
        // Trong thực tế sẽ call API login
        setLoginStatus(`Đăng nhập thành công với role: ${getRoleDisplayName(mockUser.role)}`);
        setSelectedUser(username);
      } else {
        setLoginStatus('Đăng nhập thất bại');
      }
    } catch (error) {
      setLoginStatus('Lỗi đăng nhập');
    }
  };

  const handleLogout = async () => {
    await logout();
    setLoginStatus('Đã đăng xuất');
    setSelectedUser('');
  };

  const permissions: Permission[] = [
    'view_all',
    'view_department',
    'view_room',
    'view_self',
    'manage_all_staff',
    'manage_department_staff',
    'manage_room_staff',
    'view_all_salary',
    'view_department_salary',
    'view_room_salary',
    'system_config'
  ];

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Demo Hệ thống Phân quyền</h1>
      
      {/* Login Section */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Đăng nhập Demo</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          {mockUsers.map((mockUser) => (
            <button
              key={mockUser._id}
              onClick={() => handleLogin(mockUser.username)}
              className="p-3 bg-blue-100 hover:bg-blue-200 rounded-lg text-sm"
            >
              <div className="font-semibold">{mockUser.firstName} {mockUser.lastName}</div>
              <div className="text-xs text-gray-600">{getRoleDisplayName(mockUser.role)}</div>
              <div className="text-xs text-gray-500">{mockUser.username}</div>
            </button>
          ))}
        </div>
        
        {user && (
          <button
            onClick={handleLogout}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Đăng xuất
          </button>
        )}
        
        {loginStatus && (
          <div className="mt-4 p-3 bg-gray-100 rounded-lg">
            <p>{loginStatus}</p>
          </div>
        )}
      </div>

      {/* Current User Info */}
      {user && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Thông tin User hiện tại</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Tên:</label>
              <p>{user.firstName} {user.lastName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Role:</label>
              <p>{getRoleDisplayName(user.role)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Bộ phận:</label>
              <p>{user.department || 'Không có'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Phòng ban:</label>
              <p>{user.subDepartment || 'Không có'}</p>
            </div>
          </div>
        </div>
      )}

      {/* Permission Matrix */}
      {user && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Ma trận Phân quyền</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {permissions.map((permission) => (
              <div
                key={permission}
                className={`p-3 rounded-lg border ${
                  hasPermission(permission)
                    ? 'bg-green-100 border-green-300 text-green-800'
                    : 'bg-red-100 border-red-300 text-red-800'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{permission}</span>
                  <span className="text-lg">
                    {hasPermission(permission) ? '✅' : '❌'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Department Access Test */}
      {user && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Truy cập Bộ phận</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {['quality', 'production', 'accounting'].map((dept) => (
              <div key={dept} className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2 capitalize">{dept}</h3>
                <div className="space-y-2 text-sm">
                  <div className={`flex justify-between ${canViewDepartment(dept) ? 'text-green-600' : 'text-red-600'}`}>
                    <span>Xem thông tin:</span>
                    <span>{canViewDepartment(dept) ? '✅' : '❌'}</span>
                  </div>
                  <div className={`flex justify-between ${canManageDepartmentStaff(dept) ? 'text-green-600' : 'text-red-600'}`}>
                    <span>Quản lý nhân sự:</span>
                    <span>{canManageDepartmentStaff(dept) ? '✅' : '❌'}</span>
                  </div>
                  <div className={`flex justify-between ${canViewDepartmentSalary(dept) ? 'text-green-600' : 'text-red-600'}`}>
                    <span>Xem lương:</span>
                    <span>{canViewDepartmentSalary(dept) ? '✅' : '❌'}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Protected Components Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Demo Protected Components</h2>
        
        <div className="space-y-4">
          <ProtectedComponent requiredPermission="view_all">
            <div className="p-4 bg-blue-100 rounded-lg">
              <h3 className="font-semibold">Chỉ Giám đốc mới thấy</h3>
              <p>Component này chỉ hiển thị cho user có quyền 'view_all'</p>
            </div>
          </ProtectedComponent>

          <ProtectedComponent 
            requiredPermission="manage_department_staff"
            fallback={<div className="p-4 bg-gray-100 rounded-lg text-gray-600">Bạn không có quyền quản lý nhân sự bộ phận</div>}
          >
            <div className="p-4 bg-green-100 rounded-lg">
              <h3 className="font-semibold">Quản lý nhân sự bộ phận</h3>
              <p>Component này chỉ hiển thị cho Trưởng bộ phận trở lên</p>
            </div>
          </ProtectedComponent>

          <ProtectedComponent 
            requiredRole={UserRole.EMPLOYEE}
            fallback={<div className="p-4 bg-gray-100 rounded-lg text-gray-600">Chỉ dành cho nhân viên</div>}
          >
            <div className="p-4 bg-yellow-100 rounded-lg">
              <h3 className="font-semibold">Khu vực nhân viên</h3>
              <p>Component này chỉ hiển thị cho nhân viên</p>
            </div>
          </ProtectedComponent>

          <ProtectedComponent 
            requiredPermissions={['view_department_salary', 'view_all_salary']}
            fallback={<AccessDenied message="Bạn không có quyền xem thông tin lương" />}
          >
            <div className="p-4 bg-purple-100 rounded-lg">
              <h3 className="font-semibold">Thông tin lương</h3>
              <p>Component này chỉ hiển thị cho user có quyền xem lương</p>
            </div>
          </ProtectedComponent>
        </div>
      </div>
    </div>
  );
};

export default PermissionDemo;
