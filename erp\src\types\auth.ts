// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}



export interface User {
  _id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  department?: string;
  position?: string;
  // Employee specific information
  employeeCode?: string;
  gender?: 'Nam' | 'Nữ' | 'Khác';
  subDepartment?: string;
  weight?: number;
  height?: number;
  shirtSize?: 'XS' | 'S' | 'M' | 'L' | 'XL' | 'XXL' | 'XXXL';
  pantSize?: string;
  shoeSize?: string;
  personalPhone?: string;
  bankAccount?: string;
  lockerNumber?: string;
  employeeStatus?: 'Đang làm việc' | 'Nghỉ phép' | 'Tạm nghỉ' | 'Đã nghỉ việc' | 'Thử việc';
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}



// Định nghĩa các role trong hệ thống theo cơ cấu tổ chức
export enum UserRole {
  DIRECTOR = 'director',           // Giám đốc
  DEPARTMENT_HEAD = 'department_head', // Trưởng bộ phận
  ROOM_HEAD = 'room_head',         // Trưởng phòng
  EMPLOYEE = 'employee'            // Nhân viên
}

// Định nghĩa các quyền trong hệ thống
export type Permission =
  | 'view_all'              // Xem tất cả
  | 'view_department'       // Xem bộ phận
  | 'view_room'            // Xem phòng ban
  | 'view_self'            // Xem thông tin cá nhân
  | 'manage_all_staff'     // Quản lý tất cả nhân sự
  | 'manage_department_staff' // Quản lý nhân sự bộ phận
  | 'manage_room_staff'    // Quản lý nhân sự phòng ban
  | 'update_self'          // Cập nhật thông tin cá nhân
  | 'view_all_salary'      // Xem lương tất cả
  | 'view_department_salary' // Xem lương bộ phận
  | 'view_room_salary'     // Xem lương phòng ban
  | 'view_self_salary'     // Xem lương cá nhân
  | 'system_config'        // Cấu hình hệ thống
  | 'view_reports'         // Xem báo cáo tổng hợp
  | 'view_department_reports'; // Xem báo cáo bộ phận

// Ma trận phân quyền theo role
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.DIRECTOR]: [
    'view_all',
    'manage_all_staff',
    'view_all_salary',
    'system_config',
    'view_reports',
    'view_department_reports'
  ],
  [UserRole.DEPARTMENT_HEAD]: [
    'view_department',
    'manage_department_staff',
    'view_department_salary',
    'view_department_reports',
    'view_self',
    'update_self',
    'view_self_salary'
  ],
  [UserRole.ROOM_HEAD]: [
    'view_room',
    'manage_room_staff',
    'view_room_salary',
    'view_self',
    'update_self',
    'view_self_salary'
  ],
  [UserRole.EMPLOYEE]: [
    'view_self',
    'update_self',
    'view_self_salary'
  ]
};

// Interface cho Department
export interface DepartmentInfo {
  id: string;
  name: string;
  code: string;
  description?: string;
  headId: string; // ID của trưởng bộ phận
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Interface cho Room
export interface RoomInfo {
  id: string;
  name: string;
  code: string;
  departmentId: string;
  headId: string; // ID của trưởng phòng
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum Department {
  QUALITY = 'quality',        // Chất lượng
  GENERAL = 'general',        // Tổng hợp
  BUSINESS = 'business',      // Kinh doanh
  ACCOUNTING = 'accounting',  // Kế toán
  PURCHASING = 'purchasing',  // Thu mua
  PRODUCTION = 'production',  // Sản xuất
  TECHNICAL = 'technical'     // Kỹ thuật
}

// Helper functions để check quyền
export const hasPermission = (userRole: UserRole, requiredPermission: Permission): boolean => {
  return ROLE_PERMISSIONS[userRole].includes(requiredPermission);
};

// Helper function để check multiple permissions (có ít nhất 1 quyền)
export const hasAnyPermission = (userRole: UserRole, requiredPermissions: Permission[]): boolean => {
  return requiredPermissions.some(permission => hasPermission(userRole, permission));
};

// Helper function để check tất cả permissions
export const hasAllPermissions = (userRole: UserRole, requiredPermissions: Permission[]): boolean => {
  return requiredPermissions.every(permission => hasPermission(userRole, permission));
};

// Helper function để lấy tên role hiển thị
export const getRoleDisplayName = (role: UserRole): string => {
  const roleNames = {
    [UserRole.DIRECTOR]: 'Giám đốc',
    [UserRole.DEPARTMENT_HEAD]: 'Trưởng bộ phận',
    [UserRole.ROOM_HEAD]: 'Trưởng phòng',
    [UserRole.EMPLOYEE]: 'Nhân viên'
  };
  return roleNames[role] || 'Không xác định';
};

// Helper function để lấy tên bộ phận hiển thị
export const getDepartmentDisplayName = (department: Department): string => {
  const departmentNames = {
    [Department.QUALITY]: 'Chất lượng',
    [Department.GENERAL]: 'Tổng hợp',
    [Department.BUSINESS]: 'Kinh doanh',
    [Department.ACCOUNTING]: 'Kế toán',
    [Department.PURCHASING]: 'Thu mua',
    [Department.PRODUCTION]: 'Sản xuất',
    [Department.TECHNICAL]: 'Kỹ thuật'
  };
  return departmentNames[department] || 'Không xác định';
};
